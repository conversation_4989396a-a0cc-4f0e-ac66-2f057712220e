Copyright (C) 2016 Accton Networks, Inc.

This program is free software: you can redistribute it and/or modify
It under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  
See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.

Contents of this package:
  patch - files under patch/ is for kernel and ONIE installer                 
        for the kernel:         
            config-accton-as5712_54x.patch 
                    for kernel configuration.
            driver-i2c-muxes-pca954x-always-deselect.patch 
                    for i2c_mux deselects after transaction.
            driver-patches-for-accton-as5712-fan-psu-cpld.patch
                    for as5712's fan/psu/cpld/led/sfp drivers.
        for ONIE:
            onie_installer-accton-AS5712-54X.patch 
                    for console port setting and copy util script o rootfs.
  module - Contains source code of as5712 kernel driver modules.

The late Sonic building scripts, pushed @Dec 5 2016, will automatically 
create a docker container and run building process under it.
User is not necessary to handle docker environment creation.

1. Download sonic-buildimage environment.
  - Run "git clone https://github.com/Azure/sonic-buildimage".
  - cd to sonic-buildimage and run "git submodule update --init --recursive".
2. Build kernel
  - cd ./src/sonic-linux-kernel
  - Copy patches and series from patch/kernel of this release to 
    sonic-linux-kernel/patch.
  - Build kernel by "make".
  - The built kernel package, linux-image-3.16.0-4-amd64_3.16.36-1+deb8u2_amd64.deb
    , is generated.
3. Build installer 
  - Change directory back to sonic-buildimage/.
  - Get onie_installer-accton-AS5712-54X.patch" from patch/installer.
  - Change setting for AS5712-54X by patching build_image.sh.
    "patch -p1 < onie_installer-accton-AS5712-54X.patch"
    !!NOTICE, patching onie_installer-accton-AS5712-54X.patch comments out the
    "git status" checking at build_image.sh.
  - The account and password of installed OS can be given at rules/config.
    The default user and password are "admin" & "YourPaSsWoRd" respectively.
  - Run "make configure PLATFORM=broadcom"
  - Copy the built kernel debian package to target/debs/.
    The file is linux-image-3.16.0-4-amd64_*_amd64.deb under directory
    src/sonic-linux-kernel/.
  - Run "make target/sonic-generic.bin"
  - Get the installer, target/sonic-generic.bin, to target machine and install.

All Linux kernel code is licensed under the GPLv1. All other code is
licensed under the GPLv3. Please see the LICENSE file for copies of
both licenses.

The code for integacting with Accton AS5712-54X has 2 parts, 
kernel drivers and operational script. 
The kernel drivers of peripherals are under module/ directory.
1.  These drivers are patched into kernel by 
    driver-patches-for-accton-as5712-fan-psu-cpld.patch
    Or you can build the driver under module/ by setting environment variable, 
    KERNEL_SRC, to proper linux built directory and run make. 
    It may be sonic-linux-kernel/linux-3.*/debian/build/build_amd64_none_amd64/.
2.  A operational script, accton_as5712_util.py, for device initializatian and 
    peripheral accessing should be installed at /usr/bin. 
    This script is generated by onie_installer-accton-AS5712-54X.patch.
    It's done by patching onie_installer-accton-AS5712-54X.patch at build-image.
    Run "accton_as5712_util.py install" to install drivers.

To initialize the system, run "accton_as5712_util.py install".
To clean up the drivers & devices, run "accton_as5712_util.py clean".
To dump information of sensors, run "accton_as5712_util.py show".
To dump SFP EEPROM, run "accton_as5712_util.py sff".
To set fan speed, run "accton_as5712_util.py set fan".
To enable/disable SFP emission, run "accton_as5712_util.py set sfp".
To set system LEDs' color, run "accton_as5712_util.py set led"
For more information, run "accton_as5712_util.py --help".

====================================================================
Besides applying accton_as5712_util.py to access peripherals, you can 
access peripherals by sysfs nodes directly after the installation is run.

System LED:
    There are 5 system LEDs at the lower-left corner of front panel.
    They are  loc, diag, fan, ps1, and ps2. 
    The sysfs interface color mappings are as follows:
    Brightness:
        0 => off
        1 => green
        2 => amber
        3 => red
        4 => blue
    But not all colors are available for each LED.

Fan Control:
    There are 10 fans inside 5 fan modules.
    All fans share 1 duty setting, ranged from 0~100.

Thermal sensers:
    3 temperature sensors are controlled by the lm75 kernel modules. 

PSUs:
    There 2 power supplies slot at the left/right side of the back.
    Once if a PSU is not plugged, the status of it is shown failed.

There are 48 SFP+ and 6 QSFP modules are equipped. 
Before operating on PSU and QSFP+, please make sure it is well plugged. 
Otherwise, operation is going to fail.

