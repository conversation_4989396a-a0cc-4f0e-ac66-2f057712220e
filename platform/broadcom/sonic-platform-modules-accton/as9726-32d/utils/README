Copyright (C) 2019 Accton Networks, Inc.

This program is free software: you can redistribute it and/or modify
It under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.

This program is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  
See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License
along with this program.  If not, see <http://www.gnu.org/licenses/>.

All Linux kernel code is licensed under the GPLv1. All other code is
licensed under the GPLv3. Please see the LICENSE file for copies of
both licenses.

The code for integacting with Accton AS9726-32D has 2 parts, 
kernel drivers and operational script. 
The kernel drivers of peripherals are under module/ directory.
1.  These drivers are at module dir.
2.  A operational script, accton_as9726_32d_util.py, for device initializatian and 
    peripheral accessing should be installed at /usr/bin. 
    Run "accton_as9726_32d_util.py install" to install drivers.

To initialize the system, run "accton_as9726_32d_util.py install".
To clean up the drivers & devices, run "accton_as9726_32d_util.py clean".
To dump information of sensors, run "accton_as9726_32d_util.py show".
To dump SFP EEPROM, run "accton_as9726_32d_util.py sff".
To set fan speed, run "accton_as9726_32d_util.py set fan".
To enable/disable SFP emission, run "accton_as9726_32d_util.py set sfp".
To set system LEDs' color, run "accton_as9726_32d_util.py set led"
For more information, run "accton_as9726_32d_util.py --help".

====================================================================
Besides applying accton_as9726_32d_util.py to access peripherals, you can 
access peripherals by sysfs nodes directly after the installation is run.

System LED:
    There are 5 system LEDs at the lower-left corner of front panel.
    They are  loc, diag, psu1, psu2 and fan. 
    The sysfs interface color mappings are as follows:
    Brightness:
        0 => off
        1 => green
        2 => amber
    But not all colors are available for each LED.

Fan Control:
    There are 12 fans inside 6 fan modules.
    All fans share 1 duty setting, ranged from 0~100.

Thermal sensers:
    6 temperature sensors are controlled by the lm75 kernel modules. 

PSUs:
    There 2 power supplies slot at the left/right side of the back.
    Once if a PSU is not plugged, the status of it is shown failed.

There are 32 QSFP56-DD and 2 SFP+ modules are equipped. 
Before operating on PSU and QSFP+, please make sure it is well plugged. 
Otherwise, operation is going to fail.

