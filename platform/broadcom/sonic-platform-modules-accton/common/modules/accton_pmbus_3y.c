/*
 * Hardware monitoring driver for 3Y power devices.
 *
 * Copyright (C) 2018 Accton Technology Corporation.
 * <PERSON> <<EMAIL>>
 *
 * Based on pmbus.c, pmbus.h and pmbus_core.c.
 * Copyright 2012 Guenter <PERSON>/<PERSON> AB.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 675 Mass Ave, Cambridge, MA 02139, USA.
 */

#include <linux/kernel.h>
#include <linux/module.h>
#include <linux/init.h>
#include <linux/err.h>
#include <linux/slab.h>
#include <linux/i2c.h>
#include <linux/hwmon.h>
#include <linux/hwmon-sysfs.h>
#include <linux/jiffies.h>
#include <linux/pmbus.h>
#include "pmbus.h"


enum chips {
    YM2651,
    YM2401,
    YM2851,
    LIMITED_CHIPS,
};


/*
 * Number of additional attribute pointers to allocate
 * with each call to krealloc
 */
#define PMBUS_ATTR_ALLOC_SIZE	32

/*
 * Index into status register array, per status register group
 */
#define PB_STATUS_BASE		0
#define PB_STATUS_VOUT_BASE	(PB_STATUS_BASE + PMBUS_PAGES)
#define PB_STATUS_IOUT_BASE	(PB_STATUS_VOUT_BASE + PMBUS_PAGES)
#define PB_STATUS_FAN_BASE	(PB_STATUS_IOUT_BASE + PMBUS_PAGES)
#define PB_STATUS_FAN34_BASE	(PB_STATUS_FAN_BASE + PMBUS_PAGES)
#define PB_STATUS_TEMP_BASE	(PB_STATUS_FAN34_BASE + PMBUS_PAGES)
#define PB_STATUS_INPUT_BASE	(PB_STATUS_TEMP_BASE + PMBUS_PAGES)
#define PB_STATUS_VMON_BASE	(PB_STATUS_INPUT_BASE + 1)

#define PB_NUM_STATUS_REG	(PB_STATUS_VMON_BASE + 1)

#define PMBUS_NAME_SIZE		24

struct pmbus_sensor {
    struct pmbus_sensor *next;
    char name[PMBUS_NAME_SIZE];	/* sysfs sensor name */
    struct device_attribute attribute;
    u8 page;		/* page number */
    u16 reg;		/* register */
    enum pmbus_sensor_classes class;	/* sensor class */
    bool update;		/* runtime sensor update needed */
    int data;		/* Sensor data.
				   Negative if there was a read error */
};
#define to_pmbus_sensor(_attr) \
	container_of(_attr, struct pmbus_sensor, attribute)

struct pmbus_boolean {
    char name[PMBUS_NAME_SIZE];	/* sysfs boolean name */
    struct sensor_device_attribute attribute;
    struct pmbus_sensor *s1;
    struct pmbus_sensor *s2;
};
#define to_pmbus_boolean(_attr) \
	container_of(_attr, struct pmbus_boolean, attribute)

struct pmbus_label {
    char name[PMBUS_NAME_SIZE];	/* sysfs label name */
    struct device_attribute attribute;
    char label[PMBUS_NAME_SIZE];	/* label */
};
#define to_pmbus_label(_attr) \
	container_of(_attr, struct pmbus_label, attribute)

struct pmbus_data {
    struct device *dev;
    struct device *hwmon_dev;

    u32 flags;		/* from platform data */

    int exponent[PMBUS_PAGES];
    /* linear mode: exponent for output voltages */

    const struct pmbus_driver_info *info;

    int max_attributes;
    int num_attributes;
    struct attribute_group group;
    const struct attribute_group *groups[2];

    struct pmbus_sensor *sensors;

    struct mutex update_lock;
    bool valid;
    unsigned long last_updated;	/* in jiffies */

    /*
     * A single status register covers multiple attributes,
     * so we keep them all together.
     */
    u8 status[PB_NUM_STATUS_REG];
    u8 status_register;

    u8 currpage;
    bool linear_16;
};


static int limited_models(const struct i2c_device_id *id);

int _pmbus_set_page(struct i2c_client *client, u8 page)
{
    struct pmbus_data *data = i2c_get_clientdata(client);
    int rv = 0;
    int newpage;

    if (page != data->currpage) {
        rv = i2c_smbus_write_byte_data(client, PMBUS_PAGE, page);
        newpage = i2c_smbus_read_byte_data(client, PMBUS_PAGE);
        if (newpage != page)
            rv = -EIO;
        else
            data->currpage = page;
    }
    return 0;
}

/*
 * _pmbus_write_byte() is similar to pmbus_write_byte(), but checks if
 * a device specific mapping function exists and calls it if necessary.
 */
static int _pmbus_write_byte(struct i2c_client *client, int page, u8 value)
{
    struct pmbus_data *data = i2c_get_clientdata(client);
    const struct pmbus_driver_info *info = data->info;
    int status;

    if (info->write_byte) {
        status = info->write_byte(client, page, value);
        if (status != -ENODATA)
            return status;
    }
    /*Ignore page*/
    return i2c_smbus_write_byte(client, value);;
}

/*
 * _pmbus_write_word_data() is similar to pmbus_write_word_data(), but checks if
 * a device specific mapping function exists and calls it if necessary.
 */
static int _pmbus_write_word_data(struct i2c_client *client, int page, int reg,
                                  u16 word)
{
    if (reg >= PMBUS_VIRT_BASE)
        return -ENXIO;
    /*Ignore page*/
    return i2c_smbus_write_word_data(client, reg, word);
}

/*
 * _pmbus_read_word_data() is similar to pmbus_read_word_data(), but checks if
 * a device specific mapping function exists and calls it if necessary.
 */
static int _pmbus_read_word_data(struct i2c_client *client, int page, int reg)
{
    /*Ignore page*/
    return i2c_smbus_read_word_data(client, reg);;
}

/*
 * _pmbus_read_byte_data() is similar to pmbus_read_byte_data(), but checks if
 * a device specific mapping function exists and calls it if necessary.
 */
static int _pmbus_read_byte_data(struct i2c_client *client, int page, int reg)
{
    if (reg >= PMBUS_VIRT_BASE)
        return -ENXIO;

    /*Ignore page*/
    return i2c_smbus_read_byte_data(client, reg);
}

static void pmbus_clear_fault_page(struct i2c_client *client, int page)
{
    _pmbus_write_byte(client, page, PMBUS_CLEAR_FAULTS);
}


static int pmbus_check_status_cml(struct i2c_client *client)
{
    struct pmbus_data *data = i2c_get_clientdata(client);
    int status, status2;

    status = _pmbus_read_byte_data(client, -1, data->status_register);
    if (status < 0 || (status & PB_STATUS_CML)) {
        status2 = _pmbus_read_byte_data(client, -1, PMBUS_STATUS_CML);
        if (status2 < 0 || (status2 & PB_CML_FAULT_INVALID_COMMAND))
            return -EIO;
    }
    return 0;
}

static bool pmbus_check_register(struct i2c_client *client,
                                 int (*func)(struct i2c_client *client,
                                         int page, int reg),
                                 int page, int reg)
{
    int rv;
    struct pmbus_data *data = i2c_get_clientdata(client);

    if((data->flags & PMBUS_SKIP_STATUS_CHECK))
        return 1;

    rv = func(client, page, reg);
    if (rv >= 0)
        rv = pmbus_check_status_cml(client);
    pmbus_clear_fault_page(client, -1);
    return rv >= 0;
}

static struct _pmbus_status {
    u32 func;
    u16 base;
    u16 reg;
} pmbus_status[] = {
    { PMBUS_HAVE_STATUS_VOUT, PB_STATUS_VOUT_BASE, PMBUS_STATUS_VOUT },
    { PMBUS_HAVE_STATUS_IOUT, PB_STATUS_IOUT_BASE, PMBUS_STATUS_IOUT },
    {   PMBUS_HAVE_STATUS_TEMP, PB_STATUS_TEMP_BASE,
        PMBUS_STATUS_TEMPERATURE
    },
    { PMBUS_HAVE_STATUS_FAN12, PB_STATUS_FAN_BASE, PMBUS_STATUS_FAN_12 },
    { PMBUS_HAVE_STATUS_FAN34, PB_STATUS_FAN34_BASE, PMBUS_STATUS_FAN_34 },
};

void _pmbus_clear_faults(struct i2c_client *client)
{
    struct pmbus_data *data = i2c_get_clientdata(client);
    int i;

    for (i = 0; i < data->info->pages; i++)
        pmbus_clear_fault_page(client, i);
}
static struct pmbus_data *pmbus_update_device(struct device *dev)
{
    struct i2c_client *client = to_i2c_client(dev->parent);
    struct pmbus_data *data = i2c_get_clientdata(client);
    const struct pmbus_driver_info *info = data->info;
    struct pmbus_sensor *sensor;

    mutex_lock(&data->update_lock);
    if (time_after(jiffies, data->last_updated + HZ) || !data->valid) {
        int i, j;

        for (i = 0; i < info->pages; i++) {
            data->status[PB_STATUS_BASE + i]
                = _pmbus_read_byte_data(client, i,
                                        data->status_register);
            for (j = 0; j < ARRAY_SIZE(pmbus_status); j++) {
                struct _pmbus_status *s = &pmbus_status[j];

                if (!(info->func[i] & s->func))
                    continue;
                data->status[s->base + i]
                    = _pmbus_read_byte_data(client, i,
                                            s->reg);
            }
        }

        if (info->func[0] & PMBUS_HAVE_STATUS_INPUT)
            data->status[PB_STATUS_INPUT_BASE]
                = _pmbus_read_byte_data(client, 0,
                                        PMBUS_STATUS_INPUT);

        if (info->func[0] & PMBUS_HAVE_STATUS_VMON)
            data->status[PB_STATUS_VMON_BASE]
                = _pmbus_read_byte_data(client, 0,
                                        PMBUS_VIRT_STATUS_VMON);

        for (sensor = data->sensors; sensor; sensor = sensor->next) {
            if (!data->valid || sensor->update)
                sensor->data
                    = _pmbus_read_word_data(client,
                                            sensor->page,
                                            sensor->reg);
        }
        _pmbus_clear_faults(client);
        data->last_updated = jiffies;
        data->valid = 1;
    }
    mutex_unlock(&data->update_lock);
    return data;
}

/*
 * Convert linear sensor values to milli- or micro-units
 * depending on sensor type.
 */
static long pmbus_reg2data_linear(struct pmbus_data *data,
                                  struct pmbus_sensor *sensor)
{
    s16 exponent;
    s32 mantissa;
    long val;

    /* LINEAR16 */
    if ( data->linear_16 &&
            sensor->class == PSC_VOLTAGE_OUT) {
        exponent = data->exponent[sensor->page];
        mantissa = (u16) sensor->data;
    } else
    {   /* LINEAR11 */
        exponent = ((s16)sensor->data) >> 11;
        mantissa = ((s16)((sensor->data & 0x7ff) << 5)) >> 5;
    }

    val = mantissa;

    /* scale result to milli-units for all sensors except fans */
    if (sensor->class != PSC_FAN)
        val = val * 1000L;

    /* scale result to micro-units for power sensors */
    if (sensor->class == PSC_POWER)
        val = val * 1000L;

    if (exponent >= 0)
        val <<= exponent;
    else
        val >>= -exponent;

    return val;
}

/*
 * Convert direct sensor values to milli- or micro-units
 * depending on sensor type.
 */
static long pmbus_reg2data_direct(struct pmbus_data *data,
                                  struct pmbus_sensor *sensor)
{
    long val = (s16) sensor->data;
    long m, b, R;

    m = data->info->m[sensor->class];
    b = data->info->b[sensor->class];
    R = data->info->R[sensor->class];

    if (m == 0)
        return 0;

    /* X = 1/m * (Y * 10^-R - b) */
    R = -R;
    /* scale result to milli-units for everything but fans */
    if (sensor->class != PSC_FAN) {
        R += 3;
        b *= 1000;
    }

    /* scale result to micro-units for power sensors */
    if (sensor->class == PSC_POWER) {
        R += 3;
        b *= 1000;
    }

    while (R > 0) {
        val *= 10;
        R--;
    }
    while (R < 0) {
        val = DIV_ROUND_CLOSEST(val, 10);
        R++;
    }

    return (val - b) / m;
}

/*
 * Convert VID sensor values to milli- or micro-units
 * depending on sensor type.
 * We currently only support VR11.
 */
static long pmbus_reg2data_vid(struct pmbus_data *data,
                               struct pmbus_sensor *sensor)
{
    long val = sensor->data;

    if (val < 0x02 || val > 0xb2)
        return 0;
    return DIV_ROUND_CLOSEST(160000 - (val - 2) * 625, 100);
}

static long pmbus_reg2data(struct pmbus_data *data, struct pmbus_sensor *sensor)
{
    long val;

    switch (data->info->format[sensor->class]) {
    case direct:
        val = pmbus_reg2data_direct(data, sensor);
        break;
    case vid:
        val = pmbus_reg2data_vid(data, sensor);
        break;
    case linear:
    default:
        val = pmbus_reg2data_linear(data, sensor);
        break;
    }
    return val;
}

#define MAX_MANTISSA	(1023 * 1000)
#define MIN_MANTISSA	(511 * 1000)

static u16 pmbus_data2reg_linear(struct pmbus_data *data,
                                 struct pmbus_sensor *sensor, long val)
{
    s16 exponent = 0, mantissa;
    bool negative = false;

    /* simple case */
    if (val == 0)
        return 0;

    if (data->linear_16 &&
            sensor->class == PSC_VOLTAGE_OUT) {
        /* LINEAR16 does not support negative voltages */
        if (val < 0)
            return 0;

        /*
         * For a static exponents, we don't have a choice
         * but to adjust the value to it.
         */
        if (data->exponent[sensor->page] < 0)
            val <<= -data->exponent[sensor->page];
        else
            val >>= data->exponent[sensor->page];
        val = DIV_ROUND_CLOSEST(val, 1000);
        return val & 0xffff;
    }

    if (val < 0) {
        negative = true;
        val = -val;
    }

    /* Power is in uW. Convert to mW before converting. */
    if (sensor->class == PSC_POWER)
        val = DIV_ROUND_CLOSEST(val, 1000L);

    /*
     * For simplicity, convert fan data to milli-units
     * before calculating the exponent.
     */
    if (sensor->class == PSC_FAN)
        val = val * 1000;

    /* Reduce large mantissa until it fits into 10 bit */
    while (val >= MAX_MANTISSA && exponent < 15) {
        exponent++;
        val >>= 1;
    }
    /* Increase small mantissa to improve precision */
    while (val < MIN_MANTISSA && exponent > -15) {
        exponent--;
        val <<= 1;
    }

    /* Convert mantissa from milli-units to units */
    mantissa = DIV_ROUND_CLOSEST(val, 1000);

    /* Ensure that resulting number is within range */
    if (mantissa > 0x3ff)
        mantissa = 0x3ff;

    /* restore sign */
    if (negative)
        mantissa = -mantissa;

    /* Convert to 5 bit exponent, 11 bit mantissa */
    return (mantissa & 0x7ff) | ((exponent << 11) & 0xf800);
}

static u16 pmbus_data2reg_direct(struct pmbus_data *data,
                                 struct pmbus_sensor *sensor, long val)
{
    long m, b, R;

    m = data->info->m[sensor->class];
    b = data->info->b[sensor->class];
    R = data->info->R[sensor->class];

    /* Power is in uW. Adjust R and b. */
    if (sensor->class == PSC_POWER) {
        R -= 3;
        b *= 1000;
    }

    /* Calculate Y = (m * X + b) * 10^R */
    if (sensor->class != PSC_FAN) {
        R -= 3;		/* Adjust R and b for data in milli-units */
        b *= 1000;
    }
    val = val * m + b;

    while (R > 0) {
        val *= 10;
        R--;
    }
    while (R < 0) {
        val = DIV_ROUND_CLOSEST(val, 10);
        R++;
    }

    return val;
}

static u16 pmbus_data2reg_vid(struct pmbus_data *data,
                              struct pmbus_sensor *sensor, long val)
{
    val = clamp_val(val, 500, 1600);

    return 2 + DIV_ROUND_CLOSEST((1600 - val) * 100, 625);
}

static u16 pmbus_data2reg(struct pmbus_data *data,
                          struct pmbus_sensor *sensor, long val)
{
    u16 regval;

    switch (data->info->format[sensor->class]) {
    case direct:
        regval = pmbus_data2reg_direct(data, sensor, val);
        break;
    case vid:
        regval = pmbus_data2reg_vid(data, sensor, val);
        break;
    case linear:
    default:
        regval = pmbus_data2reg_linear(data, sensor, val);
        break;
    }
    return regval;
}

/*
 * Return boolean calculated from converted data.
 * <index> defines a status register index and mask.
 * The mask is in the lower 8 bits, the register index is in bits 8..23.
 *
 * The associated pmbus_boolean structure contains optional pointers to two
 * sensor attributes. If specified, those attributes are compared against each
 * other to determine if a limit has been exceeded.
 *
 * If the sensor attribute pointers are NULL, the function returns true if
 * (status[reg] & mask) is true.
 *
 * If sensor attribute pointers are provided, a comparison against a specified
 * limit has to be performed to determine the boolean result.
 * In this case, the function returns true if v1 >= v2 (where v1 and v2 are
 * sensor values referenced by sensor attribute pointers s1 and s2).
 *
 * To determine if an object exceeds upper limits, specify <s1,s2> = <v,limit>.
 * To determine if an object exceeds lower limits, specify <s1,s2> = <limit,v>.
 *
 * If a negative value is stored in any of the referenced registers, this value
 * reflects an error code which will be returned.
 */
static int pmbus_get_boolean(struct pmbus_data *data, struct pmbus_boolean *b,
                             int index)
{
    struct pmbus_sensor *s1 = b->s1;
    struct pmbus_sensor *s2 = b->s2;
    u16 reg = (index >> 8) & 0xffff;
    u8 mask = index & 0xff;
    int ret, status;
    u8 regval;

    status = data->status[reg];
    if (status < 0)
        return status;

    regval = status & mask;
    if (!s1 && !s2) {
        ret = !!regval;
    } else if (!s1 || !s2) {
        WARN(1, "Bad boolean descriptor %p: s1=%p, s2=%p\n", b, s1, s2);
        return 0;
    } else {
        long v1, v2;

        if (s1->data < 0)
            return s1->data;
        if (s2->data < 0)
            return s2->data;

        v1 = pmbus_reg2data(data, s1);
        v2 = pmbus_reg2data(data, s2);
        ret = !!(regval && v1 >= v2);
    }
    return ret;
}

static ssize_t pmbus_show_boolean(struct device *dev,
                                  struct device_attribute *da, char *buf)
{
    struct sensor_device_attribute *attr = to_sensor_dev_attr(da);
    struct pmbus_boolean *boolean = to_pmbus_boolean(attr);
    struct pmbus_data *data = pmbus_update_device(dev);
    int val;

    val = pmbus_get_boolean(data, boolean, attr->index);
    if (val < 0) {
        return snprintf(buf, PAGE_SIZE, "%d\n", 1);
    }
    return snprintf(buf, PAGE_SIZE, "%d\n", val);
}

static ssize_t pmbus_show_sensor(struct device *dev,
                                 struct device_attribute *devattr, char *buf)
{
    struct pmbus_data *data = pmbus_update_device(dev);
    struct pmbus_sensor *sensor = to_pmbus_sensor(devattr);


    if (sensor->data < 0) {
        return snprintf(buf, PAGE_SIZE, "%d\n", 0);
    }


    return snprintf(buf, PAGE_SIZE, "%ld\n", pmbus_reg2data(data, sensor));
}

static ssize_t pmbus_set_sensor(struct device *dev,
                                struct device_attribute *devattr,
                                const char *buf, size_t count)
{
    struct i2c_client *client = to_i2c_client(dev->parent);
    struct pmbus_data *data = i2c_get_clientdata(client);
    struct pmbus_sensor *sensor = to_pmbus_sensor(devattr);
    ssize_t rv = count;
    long val = 0;
    int ret;
    u16 regval;

    if (kstrtol(buf, 10, &val) < 0)
        return -EINVAL;

    mutex_lock(&data->update_lock);
    regval = pmbus_data2reg(data, sensor, val);
    ret = _pmbus_write_word_data(client, sensor->page, sensor->reg, regval);
    if (ret < 0)
        rv = ret;
    else
        sensor->data = regval;
    mutex_unlock(&data->update_lock);
    return rv;
}

static ssize_t pmbus_show_label(struct device *dev,
                                struct device_attribute *da, char *buf)
{
    struct pmbus_label *label = to_pmbus_label(da);

    return snprintf(buf, PAGE_SIZE, "%s\n", label->label);
}

static int pmbus_add_attribute(struct pmbus_data *data, struct attribute *attr)
{
    if (data->num_attributes >= data->max_attributes - 1) {
        int new_max_attrs = data->max_attributes + PMBUS_ATTR_ALLOC_SIZE;
        void *new_attrs = krealloc(data->group.attrs,
                                   new_max_attrs * sizeof(void *),
                                   GFP_KERNEL);
        if (!new_attrs)
            return -ENOMEM;
        data->group.attrs = new_attrs;
        data->max_attributes = new_max_attrs;
    }



    data->group.attrs[data->num_attributes++] = attr;
    data->group.attrs[data->num_attributes] = NULL;
    return 0;
}

static void pmbus_dev_attr_init(struct device_attribute *dev_attr,
                                const char *name,
                                umode_t mode,
                                ssize_t (*show)(struct device *dev,
                                        struct device_attribute *attr,
                                        char *buf),
                                ssize_t (*store)(struct device *dev,
                                        struct device_attribute *attr,
                                        const char *buf, size_t count))
{
    sysfs_attr_init(&dev_attr->attr);
    dev_attr->attr.name = name;
    dev_attr->attr.mode = mode;
    dev_attr->show = show;
    dev_attr->store = store;
}

static void pmbus_attr_init(struct sensor_device_attribute *a,
                            const char *name,
                            umode_t mode,
                            ssize_t (*show)(struct device *dev,
                                    struct device_attribute *attr,
                                    char *buf),
                            ssize_t (*store)(struct device *dev,
                                    struct device_attribute *attr,
                                    const char *buf, size_t count),
                            int idx)
{
    pmbus_dev_attr_init(&a->dev_attr, name, mode, show, store);
    a->index = idx;
}

static int pmbus_add_boolean(struct pmbus_data *data,
                             const char *name, const char *type, int seq,
                             struct pmbus_sensor *s1,
                             struct pmbus_sensor *s2,
                             u16 reg, u8 mask)
{
    struct pmbus_boolean *boolean;
    struct sensor_device_attribute *a;

    boolean = devm_kzalloc(data->dev, sizeof(*boolean), GFP_KERNEL);
    if (!boolean)
        return -ENOMEM;

    a = &boolean->attribute;

    snprintf(boolean->name, sizeof(boolean->name), "%s%d_%s",
             name, seq, type);
    boolean->s1 = s1;
    boolean->s2 = s2;
    pmbus_attr_init(a, boolean->name, S_IRUGO, pmbus_show_boolean, NULL,
                    (reg << 8) | mask);

    return pmbus_add_attribute(data, &a->dev_attr.attr);
}

static struct pmbus_sensor *pmbus_add_sensor(struct pmbus_data *data,
        const char *name, const char *type,
        int seq, int page, int reg,
        enum pmbus_sensor_classes class,
        bool update, bool readonly)
{
    struct pmbus_sensor *sensor;
    struct device_attribute *a;

    sensor = devm_kzalloc(data->dev, sizeof(*sensor), GFP_KERNEL);
    if (!sensor)
        return NULL;
    a = &sensor->attribute;

    snprintf(sensor->name, sizeof(sensor->name), "%s%d_%s",
             name, seq, type);
    sensor->page = page;
    sensor->reg = reg;
    sensor->class = class;
    sensor->update = update;
    pmbus_dev_attr_init(a, sensor->name,
                        readonly ? S_IRUGO : S_IRUGO | S_IWUSR,
                        pmbus_show_sensor, pmbus_set_sensor);

    if (pmbus_add_attribute(data, &a->attr))
        return NULL;

    sensor->next = data->sensors;
    data->sensors = sensor;

    return sensor;
}

static int pmbus_add_label(struct pmbus_data *data,
                           const char *name, int seq,
                           const char *lstring, int index)
{
    struct pmbus_label *label;
    struct device_attribute *a;

    label = devm_kzalloc(data->dev, sizeof(*label), GFP_KERNEL);
    if (!label)
        return -ENOMEM;

    a = &label->attribute;

    snprintf(label->name, sizeof(label->name), "%s%d_label", name, seq);
    if (!index)
        strncpy(label->label, lstring, sizeof(label->label) - 1);
    else
        snprintf(label->label, sizeof(label->label), "%s%d", lstring,
                 index);

    pmbus_dev_attr_init(a, label->name, S_IRUGO, pmbus_show_label, NULL);
    return pmbus_add_attribute(data, &a->attr);
}

/*
 * Search for attributes. Allocate sensors, booleans, and labels as needed.
 */

/*
 * The pmbus_limit_attr structure describes a single limit attribute
 * and its associated alarm attribute.
 */
struct pmbus_limit_attr {
    u16 reg;		/* Limit register */
    u16 sbit;		/* Alarm attribute status bit */
    bool update;		/* True if register needs updates */
    bool low;		/* True if low limit; for limits with compare
				   functions only */
    const char *attr;	/* Attribute name */
    const char *alarm;	/* Alarm attribute name */
};

/*
 * The pmbus_sensor_attr structure describes one sensor attribute. This
 * description includes a reference to the associated limit attributes.
 */
struct pmbus_sensor_attr {
    u16 reg;			/* sensor register */
    u8 gbit;			/* generic status bit */
    u8 nlimit;			/* # of limit registers */
    enum pmbus_sensor_classes class;/* sensor class */
    const char *label;		/* sensor label */
    bool paged;			/* true if paged sensor */
    bool update;			/* true if update needed */
    bool compare;			/* true if compare function needed */
    u32 func;			/* sensor mask */
    u32 sfunc;			/* sensor status mask */
    int sbase;			/* status base register */
    const struct pmbus_limit_attr *limit;/* limit registers */
};

/*
 * Add a set of limit attributes and, if supported, the associated
 * alarm attributes.
 * returns 0 if no alarm register found, 1 if an alarm register was found,
 * < 0 on errors.
 */
static int pmbus_add_limit_attrs(struct i2c_client *client,
                                 struct pmbus_data *data,
                                 const struct pmbus_driver_info *info,
                                 const char *name, int index, int page,
                                 struct pmbus_sensor *base,
                                 const struct pmbus_sensor_attr *attr)
{
    const struct pmbus_limit_attr *l = attr->limit;
    int nlimit = attr->nlimit;
    int have_alarm = 0;
    int i, ret;
    struct pmbus_sensor *curr;

    for (i = 0; i < nlimit; i++) {
        if (_pmbus_check_word_register(client, page, l->reg))
        {
            curr = pmbus_add_sensor(data, name, l->attr, index,
                                    page, l->reg, attr->class,
                                    attr->update || l->update,
                                    false);
            if (!curr)
                return -ENOMEM;
            if (l->sbit && (info->func[page] & attr->sfunc)) {
                ret = pmbus_add_boolean(data, name,
                                        l->alarm, index,
                                        attr->compare ?  l->low ? curr : base
                                        : NULL,
                                        attr->compare ? l->low ? base : curr
                                        : NULL,
                                        attr->sbase + page, l->sbit);
                if (ret)
                    return ret;
                have_alarm = 1;
            }
        }
        l++;
    }
    return have_alarm;
}

bool _pmbus_check_byte_register(struct i2c_client *client, int page, int reg)
{
    return pmbus_check_register(client, _pmbus_read_byte_data, page, reg);
}


bool _pmbus_check_word_register(struct i2c_client *client, int page, int reg)
{
    return pmbus_check_register(client, _pmbus_read_word_data, page, reg);
}

static int pmbus_add_sensor_attrs_one(struct i2c_client *client,
                                      struct pmbus_data *data,
                                      const struct pmbus_driver_info *info,
                                      const char *name,
                                      int index, int page,
                                      const struct pmbus_sensor_attr *attr)
{
    struct pmbus_sensor *base;
    int ret;

    if (attr->label) {
        ret = pmbus_add_label(data, name, index, attr->label,
                              attr->paged ? page + 1 : 0);
        if (ret)
            return ret;
    }
    base = pmbus_add_sensor(data, name, "input", index, page, attr->reg,
                            attr->class, true, true);


    if (!base)
        return -ENOMEM;

    if (attr->sfunc) {
        ret = pmbus_add_limit_attrs(client, data, info, name,
                                    index, page, base, attr);
        if (ret < 0)
            return ret;
        /*
         * Add generic alarm attribute only if there are no individual
         * alarm attributes, if there is a global alarm bit, and if
         * the generic status register for this page is accessible.
         */
        if (!ret && attr->gbit &&
                _pmbus_check_byte_register(client, page,
                                           data->status_register)) {
            ret = pmbus_add_boolean(data, name, "alarm", index,
                                    NULL, NULL,
                                    PB_STATUS_BASE + page,
                                    attr->gbit);
            if (ret)
                return ret;
        }
    }
    return 0;
}

static int pmbus_add_sensor_attrs(struct i2c_client *client,
                                  struct pmbus_data *data,
                                  const char *name,
                                  const struct pmbus_sensor_attr *attrs,
                                  int nattrs)
{
    const struct pmbus_driver_info *info = data->info;
    int index, i;
    int ret;

    index = 1;
    for (i = 0; i < nattrs; i++) {
        int page, pages;

        pages = attrs->paged ? info->pages : 1;
        for (page = 0; page < pages; page++) {
            if (!(info->func[page] & attrs->func))
                continue;
            ret = pmbus_add_sensor_attrs_one(client, data, info,
                                             name, index, page,
                                             attrs);
            if (ret)
                return ret;
            index++;
        }
        attrs++;
    }
    return 0;
}

static const struct pmbus_limit_attr vin_limit_attrs[] = {
    {
        .reg = PMBUS_VIN_UV_WARN_LIMIT,
        .attr = "min",
        .alarm = "min_alarm",
        .sbit = PB_VOLTAGE_UV_WARNING,
    }, {
        .reg = PMBUS_VIN_UV_FAULT_LIMIT,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_VOLTAGE_UV_FAULT,
    }, {
        .reg = PMBUS_VIN_OV_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_VOLTAGE_OV_WARNING,
    }, {
        .reg = PMBUS_VIN_OV_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_VOLTAGE_OV_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_VIN_AVG,
        .update = true,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_VIN_MIN,
        .update = true,
        .attr = "lowest",
    }, {
        .reg = PMBUS_VIRT_READ_VIN_MAX,
        .update = true,
        .attr = "highest",
    }, {
        .reg = PMBUS_VIRT_RESET_VIN_HISTORY,
        .attr = "reset_history",
    },
};

static const struct pmbus_limit_attr vmon_limit_attrs[] = {
    {
        .reg = PMBUS_VIRT_VMON_UV_WARN_LIMIT,
        .attr = "min",
        .alarm = "min_alarm",
        .sbit = PB_VOLTAGE_UV_WARNING,
    }, {
        .reg = PMBUS_VIRT_VMON_UV_FAULT_LIMIT,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_VOLTAGE_UV_FAULT,
    }, {
        .reg = PMBUS_VIRT_VMON_OV_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_VOLTAGE_OV_WARNING,
    }, {
        .reg = PMBUS_VIRT_VMON_OV_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_VOLTAGE_OV_FAULT,
    }
};

static const struct pmbus_limit_attr vout_limit_attrs[] = {
    {
        .reg = PMBUS_VOUT_UV_WARN_LIMIT,
        .attr = "min",
        .alarm = "min_alarm",
        .sbit = PB_VOLTAGE_UV_WARNING,
    }, {
        .reg = PMBUS_VOUT_UV_FAULT_LIMIT,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_VOLTAGE_UV_FAULT,
    }, {
        .reg = PMBUS_VOUT_OV_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_VOLTAGE_OV_WARNING,
    }, {
        .reg = PMBUS_VOUT_OV_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_VOLTAGE_OV_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_VOUT_AVG,
        .update = true,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_VOUT_MIN,
        .update = true,
        .attr = "lowest",
    }, {
        .reg = PMBUS_VIRT_READ_VOUT_MAX,
        .update = true,
        .attr = "highest",
    }, {
        .reg = PMBUS_VIRT_RESET_VOUT_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_sensor_attr voltage_attributes[] = {
    {
        .reg = PMBUS_READ_VIN,
        .class = PSC_VOLTAGE_IN,
        .label = "vin",
        .func = PMBUS_HAVE_VIN,
        .sfunc = PMBUS_HAVE_STATUS_INPUT,
        .sbase = PB_STATUS_INPUT_BASE,
        .gbit = PB_STATUS_VIN_UV,
        .limit = vin_limit_attrs,
        .nlimit = ARRAY_SIZE(vin_limit_attrs),
    }, {
        .reg = PMBUS_VIRT_READ_VMON,
        .class = PSC_VOLTAGE_IN,
        .label = "vmon",
        .func = PMBUS_HAVE_VMON,
        .sfunc = PMBUS_HAVE_STATUS_VMON,
        .sbase = PB_STATUS_VMON_BASE,
        .limit = vmon_limit_attrs,
        .nlimit = ARRAY_SIZE(vmon_limit_attrs),
    }, {
        .reg = PMBUS_READ_VCAP,
        .class = PSC_VOLTAGE_IN,
        .label = "vcap",
        .func = PMBUS_HAVE_VCAP,
    }, {
        .reg = PMBUS_READ_VOUT,
        .class = PSC_VOLTAGE_OUT,
        .label = "vout",
        .paged = true,
        .func = PMBUS_HAVE_VOUT,
        .sfunc = PMBUS_HAVE_STATUS_VOUT,
        .sbase = PB_STATUS_VOUT_BASE,
        .gbit = PB_STATUS_VOUT_OV,
        .limit = vout_limit_attrs,
        .nlimit = ARRAY_SIZE(vout_limit_attrs),
    }
};

/* Current attributes */

static const struct pmbus_limit_attr iin_limit_attrs[] = {
    {
        .reg = PMBUS_IIN_OC_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_IIN_OC_WARNING,
    }, {
        .reg = PMBUS_IIN_OC_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_IIN_OC_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_IIN_AVG,
        .update = true,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_IIN_MIN,
        .update = true,
        .attr = "lowest",
    }, {
        .reg = PMBUS_VIRT_READ_IIN_MAX,
        .update = true,
        .attr = "highest",
    }, {
        .reg = PMBUS_VIRT_RESET_IIN_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_limit_attr iout_limit_attrs[] = {
    {
        .reg = PMBUS_IOUT_OC_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_IOUT_OC_WARNING,
    }, {
        .reg = PMBUS_IOUT_UC_FAULT_LIMIT,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_IOUT_UC_FAULT,
    }, {
        .reg = PMBUS_IOUT_OC_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_IOUT_OC_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_IOUT_AVG,
        .update = true,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_IOUT_MIN,
        .update = true,
        .attr = "lowest",
    }, {
        .reg = PMBUS_VIRT_READ_IOUT_MAX,
        .update = true,
        .attr = "highest",
    }, {
        .reg = PMBUS_VIRT_RESET_IOUT_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_sensor_attr current_attributes[] = {
    {
        .reg = PMBUS_READ_IIN,
        .class = PSC_CURRENT_IN,
        .label = "iin",
        .func = PMBUS_HAVE_IIN,
        .sfunc = PMBUS_HAVE_STATUS_INPUT,
        .sbase = PB_STATUS_INPUT_BASE,
        .limit = iin_limit_attrs,
        .nlimit = ARRAY_SIZE(iin_limit_attrs),
    }, {
        .reg = PMBUS_READ_IOUT,
        .class = PSC_CURRENT_OUT,
        .label = "iout",
        .paged = true,
        .func = PMBUS_HAVE_IOUT,
        .sfunc = PMBUS_HAVE_STATUS_IOUT,
        .sbase = PB_STATUS_IOUT_BASE,
        .gbit = PB_STATUS_IOUT_OC,
        .limit = iout_limit_attrs,
        .nlimit = ARRAY_SIZE(iout_limit_attrs),
    }
};

/* Power attributes */

static const struct pmbus_limit_attr pin_limit_attrs[] = {
    {
        .reg = PMBUS_PIN_OP_WARN_LIMIT,
        .attr = "max",
        .alarm = "alarm",
        .sbit = PB_PIN_OP_WARNING,
    }, {
        .reg = PMBUS_VIRT_READ_PIN_AVG,
        .update = true,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_PIN_MAX,
        .update = true,
        .attr = "input_highest",
    }, {
        .reg = PMBUS_VIRT_RESET_PIN_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_limit_attr pout_limit_attrs[] = {
    {
        .reg = PMBUS_POUT_MAX,
        .attr = "cap",
        .alarm = "cap_alarm",
        .sbit = PB_POWER_LIMITING,
    }, {
        .reg = PMBUS_POUT_OP_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_POUT_OP_WARNING,
    }, {
        .reg = PMBUS_POUT_OP_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_POUT_OP_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_POUT_AVG,
        .update = true,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_POUT_MAX,
        .update = true,
        .attr = "input_highest",
    }, {
        .reg = PMBUS_VIRT_RESET_POUT_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_sensor_attr power_attributes[] = {
    {
        .reg = PMBUS_READ_PIN,
        .class = PSC_POWER,
        .label = "pin",
        .func = PMBUS_HAVE_PIN,
        .sfunc = PMBUS_HAVE_STATUS_INPUT,
        .sbase = PB_STATUS_INPUT_BASE,
        .limit = pin_limit_attrs,
        .nlimit = ARRAY_SIZE(pin_limit_attrs),
    }, {
        .reg = PMBUS_READ_POUT,
        .class = PSC_POWER,
        .label = "pout",
        .paged = true,
        .func = PMBUS_HAVE_POUT,
        .sfunc = PMBUS_HAVE_STATUS_IOUT,
        .sbase = PB_STATUS_IOUT_BASE,
        .limit = pout_limit_attrs,
        .nlimit = ARRAY_SIZE(pout_limit_attrs),
    }
};

/* Temperature atributes */

static const struct pmbus_limit_attr temp_limit_attrs[] = {
    {
        .reg = PMBUS_UT_WARN_LIMIT,
        .low = true,
        .attr = "min",
        .alarm = "min_alarm",
        .sbit = PB_TEMP_UT_WARNING,
    }, {
        .reg = PMBUS_UT_FAULT_LIMIT,
        .low = true,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_TEMP_UT_FAULT,
    }, {
        .reg = PMBUS_OT_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_TEMP_OT_WARNING,
    }, {
        .reg = PMBUS_OT_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_TEMP_OT_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_TEMP_MIN,
        .attr = "lowest",
    }, {
        .reg = PMBUS_VIRT_READ_TEMP_AVG,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_TEMP_MAX,
        .attr = "highest",
    }, {
        .reg = PMBUS_VIRT_RESET_TEMP_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_limit_attr temp_limit_attrs2[] = {
    {
        .reg = PMBUS_UT_WARN_LIMIT,
        .low = true,
        .attr = "min",
        .alarm = "min_alarm",
        .sbit = PB_TEMP_UT_WARNING,
    }, {
        .reg = PMBUS_UT_FAULT_LIMIT,
        .low = true,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_TEMP_UT_FAULT,
    }, {
        .reg = PMBUS_OT_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_TEMP_OT_WARNING,
    }, {
        .reg = PMBUS_OT_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_TEMP_OT_FAULT,
    }, {
        .reg = PMBUS_VIRT_READ_TEMP2_MIN,
        .attr = "lowest",
    }, {
        .reg = PMBUS_VIRT_READ_TEMP2_AVG,
        .attr = "average",
    }, {
        .reg = PMBUS_VIRT_READ_TEMP2_MAX,
        .attr = "highest",
    }, {
        .reg = PMBUS_VIRT_RESET_TEMP2_HISTORY,
        .attr = "reset_history",
    }
};

static const struct pmbus_limit_attr temp_limit_attrs3[] = {
    {
        .reg = PMBUS_UT_WARN_LIMIT,
        .low = true,
        .attr = "min",
        .alarm = "min_alarm",
        .sbit = PB_TEMP_UT_WARNING,
    }, {
        .reg = PMBUS_UT_FAULT_LIMIT,
        .low = true,
        .attr = "lcrit",
        .alarm = "lcrit_alarm",
        .sbit = PB_TEMP_UT_FAULT,
    }, {
        .reg = PMBUS_OT_WARN_LIMIT,
        .attr = "max",
        .alarm = "max_alarm",
        .sbit = PB_TEMP_OT_WARNING,
    }, {
        .reg = PMBUS_OT_FAULT_LIMIT,
        .attr = "crit",
        .alarm = "crit_alarm",
        .sbit = PB_TEMP_OT_FAULT,
    }
};

static const struct pmbus_sensor_attr temp_attributes[] = {
    {
        .reg = PMBUS_READ_TEMPERATURE_1,
        .class = PSC_TEMPERATURE,
        .paged = true,
        .update = true,
        .compare = true,
        .func = PMBUS_HAVE_TEMP,
        .sfunc = PMBUS_HAVE_STATUS_TEMP,
        .sbase = PB_STATUS_TEMP_BASE,
        .gbit = PB_STATUS_TEMPERATURE,
        .limit = temp_limit_attrs,
        .nlimit = ARRAY_SIZE(temp_limit_attrs),
    }, {
        .reg = PMBUS_READ_TEMPERATURE_2,
        .class = PSC_TEMPERATURE,
        .paged = true,
        .update = true,
        .compare = true,
        .func = PMBUS_HAVE_TEMP2,
        .sfunc = PMBUS_HAVE_STATUS_TEMP,
        .sbase = PB_STATUS_TEMP_BASE,
        .gbit = PB_STATUS_TEMPERATURE,
        .limit = temp_limit_attrs2,
        .nlimit = ARRAY_SIZE(temp_limit_attrs2),
    }, {
        .reg = PMBUS_READ_TEMPERATURE_3,
        .class = PSC_TEMPERATURE,
        .paged = true,
        .update = true,
        .compare = true,
        .func = PMBUS_HAVE_TEMP3,
        .sfunc = PMBUS_HAVE_STATUS_TEMP,
        .sbase = PB_STATUS_TEMP_BASE,
        .gbit = PB_STATUS_TEMPERATURE,
        .limit = temp_limit_attrs3,
        .nlimit = ARRAY_SIZE(temp_limit_attrs3),
    }
};

static const int pmbus_fan_registers[] = {
    PMBUS_READ_FAN_SPEED_1,
    PMBUS_READ_FAN_SPEED_2,
    PMBUS_READ_FAN_SPEED_3,
    PMBUS_READ_FAN_SPEED_4
};

static const int pmbus_fan_config_registers[] = {
    PMBUS_FAN_CONFIG_12,
    PMBUS_FAN_CONFIG_12,
    PMBUS_FAN_CONFIG_34,
    PMBUS_FAN_CONFIG_34
};

static const int pmbus_fan_status_registers[] = {
    PMBUS_STATUS_FAN_12,
    PMBUS_STATUS_FAN_12,
    PMBUS_STATUS_FAN_34,
    PMBUS_STATUS_FAN_34
};

static const u32 pmbus_fan_flags[] = {
    PMBUS_HAVE_FAN12,
    PMBUS_HAVE_FAN12,
    PMBUS_HAVE_FAN34,
    PMBUS_HAVE_FAN34
};

static const u32 pmbus_fan_status_flags[] = {
    PMBUS_HAVE_STATUS_FAN12,
    PMBUS_HAVE_STATUS_FAN12,
    PMBUS_HAVE_STATUS_FAN34,
    PMBUS_HAVE_STATUS_FAN34
};

/* Fans */
static int pmbus_add_fan_attributes(struct i2c_client *client,
                                    struct pmbus_data *data)
{
    const struct pmbus_driver_info *info = data->info;
    int index = 1;
    int page;
    int ret;

    for (page = 0; page < info->pages; page++) {
        int f;

        for (f = 0; f < ARRAY_SIZE(pmbus_fan_registers); f++) {
            int regval;

            if (f >= info->fan_num)
                break;

            if (!(info->func[page] & pmbus_fan_flags[f]))
                break;

            if (!_pmbus_check_word_register(client, page,
                                            pmbus_fan_registers[f]))
                break;

            /*
             * Skip fan if not installed.
             * Each fan configuration register covers multiple fans,
             * so we have to do some magic.
             */
            regval = _pmbus_read_byte_data(client, page,
                                           pmbus_fan_config_registers[f]);
            if (regval < 0 ||
                    (!(regval & (PB_FAN_1_INSTALLED >> ((f & 1) * 4)))))
                continue;

            if (pmbus_add_sensor(data, "fan", "input", index,
                                 page, pmbus_fan_registers[f],
                                 PSC_FAN, true, true) == NULL)
                return -ENOMEM;

            /*
             * Each fan status register covers multiple fans,
             * so we have to do some magic.
             */
            if ((info->func[page] & pmbus_fan_status_flags[f]) &&
                    _pmbus_check_byte_register(client,
                                               page, pmbus_fan_status_registers[f])) {
                int base;

                if (f > 1)	/* fan 3, 4 */
                    base = PB_STATUS_FAN34_BASE + page;
                else
                    base = PB_STATUS_FAN_BASE + page;
                ret = pmbus_add_boolean(data, "fan",
                                        "alarm", index, NULL, NULL, base,
                                        PB_FAN_FAN1_WARNING >> (f & 1));
                if (ret)
                    return ret;
                ret = pmbus_add_boolean(data, "fan",
                                        "fault", index, NULL, NULL, base,
                                        PB_FAN_FAN1_FAULT >> (f & 1));
                if (ret)
                    return ret;
            }
            index++;
        }
    }
    return 0;
}

static int pmbus_find_attributes(struct i2c_client *client,
                                 struct pmbus_data *data)
{
    int ret;

    /* Voltage sensors */
    ret = pmbus_add_sensor_attrs(client, data, "in", voltage_attributes,
                                 ARRAY_SIZE(voltage_attributes));
    if (ret)
        return ret;

    /* Current sensors */
    ret = pmbus_add_sensor_attrs(client, data, "curr", current_attributes,
                                 ARRAY_SIZE(current_attributes));
    if (ret)
        return ret;

    /* Power sensors */
    ret = pmbus_add_sensor_attrs(client, data, "power", power_attributes,
                                 ARRAY_SIZE(power_attributes));
    if (ret)
        return ret;

    /* Temperature sensors */
    ret = pmbus_add_sensor_attrs(client, data, "temp", temp_attributes,
                                 ARRAY_SIZE(temp_attributes));
    if (ret)
        return ret;

    /* Fans */
    ret = pmbus_add_fan_attributes(client, data);
    return ret;
}

/*
 * Identify chip parameters.
 * This function is called for all chips.
 */
static int pmbus_identify_common(struct i2c_client *client,
                                 struct pmbus_data *data, int page)
{
    int vout_mode = -1;

    if (_pmbus_check_byte_register(client, page, PMBUS_VOUT_MODE))
    {
        vout_mode = _pmbus_read_byte_data(client, page,
                                          PMBUS_VOUT_MODE);
    }

    if (vout_mode >= 0 && vout_mode != 0xff) {
        /*
         * Not all chips support the VOUT_MODE command,
         * so a failure to read it is not an error.
         */
        switch (vout_mode >> 5) {
        case 0:	/* linear mode      */
            if (data->info->format[PSC_VOLTAGE_OUT] != linear)
                return -ENODEV;

            data->exponent[page] = ((s8)(vout_mode << 3)) >> 3;
            break;
        case 1: /* VID mode         */
            if (data->info->format[PSC_VOLTAGE_OUT] != vid)
                return -ENODEV;
            break;
        case 2:	/* direct mode      */
            if (data->info->format[PSC_VOLTAGE_OUT] != direct)
                return -ENODEV;
            break;
        default:
            return -ENODEV;
        }
    }

    pmbus_clear_fault_page(client, page);
    return 0;
}

static int pmbus_init_common(struct i2c_client *client, struct pmbus_data *data,
                             struct pmbus_driver_info *info)
{
    struct device *dev = &client->dev;
    int page, ret;

    /*
     * Some PMBus chips don't support PMBUS_STATUS_BYTE, so try
     * to use PMBUS_STATUS_WORD instead if that is the case.
     * Bail out if both registers are not supported.
     */
    if(0) { /*Skip this for the i2c access may fail if PSU is not powered.*/
        data->status_register = PMBUS_STATUS_BYTE;
        ret = i2c_smbus_read_byte_data(client, PMBUS_STATUS_BYTE);
        if (ret < 0 || ret == 0xff) {
            data->status_register = PMBUS_STATUS_WORD;
            ret = i2c_smbus_read_word_data(client, PMBUS_STATUS_WORD);
            if (ret < 0 || ret == 0xffff) {
                dev_err(dev, "PMBus status register not found\n");
                return -ENODEV;
            }
        }
    }
    _pmbus_clear_faults(client);

    if (info->identify) {
        ret = (*info->identify)(client, info);
        if (ret < 0) {
            dev_err(dev, "Chip identification failed\n");
            return ret;
        }
    }

    if (info->pages <= 0 || info->pages > PMBUS_PAGES) {
        dev_err(dev, "Bad number of PMBus pages: %d\n", info->pages);
        return -ENODEV;
    }

    for (page = 0; page < info->pages; page++) {
        ret = pmbus_identify_common(client, data, page);
        if (ret < 0) {
            dev_err(dev, "Failed to identify chip capabilities\n");
            return ret;
        }
    }
    return 0;
}

int _pmbus_do_probe(struct i2c_client *client, const struct i2c_device_id *id,
                    struct pmbus_driver_info *info)
{
    struct device *dev = &client->dev;
    struct pmbus_data *data;
    int ret;

    if (!info)
        return -ENODEV;

    if (!i2c_check_functionality(client->adapter, I2C_FUNC_SMBUS_WRITE_BYTE
                                 | I2C_FUNC_SMBUS_BYTE_DATA
                                 | I2C_FUNC_SMBUS_WORD_DATA))
        return -ENODEV;

    data = devm_kzalloc(dev, sizeof(*data), GFP_KERNEL);
    if (!data)
        return -ENOMEM;

    i2c_set_clientdata(client, data);
    mutex_init(&data->update_lock);
    data->dev = dev;

    if (limited_models(id))
    {
        data->flags |= PMBUS_SKIP_STATUS_CHECK;
        info->pages = 1;
        info->fan_num = 1;
        data->linear_16 = 0;
    }
    else
    {
        info->pages = id->driver_data;
        info->fan_num = 4;
        data->linear_16 = 1;

    }

    data->info = info;

    ret = pmbus_init_common(client, data, info);
    if (ret < 0)
        return ret;

    ret = pmbus_find_attributes(client, data);
    if (ret)
        goto out_kfree;

    /*
     * If there are no attributes, something is wrong.
     * Bail out instead of trying to register nothing.
     */
    if (!data->num_attributes) {
        dev_err(dev, "No attributes found\n");
        ret = -ENODEV;
        goto out_kfree;
    }

    data->groups[0] = &data->group;
    data->hwmon_dev = hwmon_device_register_with_groups(dev, client->name,
                      data, data->groups);
    if (IS_ERR(data->hwmon_dev)) {
        ret = PTR_ERR(data->hwmon_dev);
        dev_err(dev, "Failed to register hwmon device\n");
        goto out_kfree;
    }
    return 0;

out_kfree:
    kfree(data->group.attrs);
    return ret;
}

/*
 * Find sensor groups and status registers on each page.
 */
static void pmbus_find_sensor_groups(struct i2c_client *client,
                                     struct pmbus_driver_info *info)
{
    int page;

    /* For i2c might not available when this driver installing,
       turn on all attributes but VMON and FAN34.*/
    info->func[0] |= 0x1feff;
    return ;


    /* Sensors detected on page 0 only */
    if (_pmbus_check_word_register(client, 0, PMBUS_READ_VIN))
        info->func[0] |= PMBUS_HAVE_VIN;
    if (_pmbus_check_word_register(client, 0, PMBUS_READ_VCAP))
        info->func[0] |= PMBUS_HAVE_VCAP;
    if (_pmbus_check_word_register(client, 0, PMBUS_READ_IIN))
        info->func[0] |= PMBUS_HAVE_IIN;
    if (_pmbus_check_word_register(client, 0, PMBUS_READ_PIN))
        info->func[0] |= PMBUS_HAVE_PIN;
    if (info->func[0]
            && _pmbus_check_byte_register(client, 0, PMBUS_STATUS_INPUT))
        info->func[0] |= PMBUS_HAVE_STATUS_INPUT;
    if (info->fan_num > 0)
    {
        if (_pmbus_check_byte_register(client, 0, PMBUS_FAN_CONFIG_12) &&
                _pmbus_check_word_register(client, 0, PMBUS_READ_FAN_SPEED_1)) {
            info->func[0] |= PMBUS_HAVE_FAN12;
            if (_pmbus_check_byte_register(client, 0, PMBUS_STATUS_FAN_12))
                info->func[0] |= PMBUS_HAVE_STATUS_FAN12;
        }
    }
    if (info->fan_num > 2)
    {
        if (_pmbus_check_byte_register(client, 0, PMBUS_FAN_CONFIG_34) &&
                _pmbus_check_word_register(client, 0, PMBUS_READ_FAN_SPEED_3)) {
            info->func[0] |= PMBUS_HAVE_FAN34;
            if (_pmbus_check_byte_register(client, 0, PMBUS_STATUS_FAN_34))
                info->func[0] |= PMBUS_HAVE_STATUS_FAN34;
        }
    }

    if (_pmbus_check_word_register(client, 0, PMBUS_READ_TEMPERATURE_1))
        info->func[0] |= PMBUS_HAVE_TEMP;
    if (_pmbus_check_word_register(client, 0, PMBUS_READ_TEMPERATURE_2))
        info->func[0] |= PMBUS_HAVE_TEMP2;
    if (_pmbus_check_word_register(client, 0, PMBUS_READ_TEMPERATURE_3))
        info->func[0] |= PMBUS_HAVE_TEMP3;

    if (info->func[0] & (PMBUS_HAVE_TEMP | PMBUS_HAVE_TEMP2
                         | PMBUS_HAVE_TEMP3)
            && _pmbus_check_byte_register(client, 0,
                                          PMBUS_STATUS_TEMPERATURE))
        info->func[0] |= PMBUS_HAVE_STATUS_TEMP;

    /* Sensors detected on all pages */
    for (page = 0; page < info->pages; page++) {
        if (_pmbus_check_word_register(client, page, PMBUS_READ_VOUT)) {
            info->func[page] |= PMBUS_HAVE_VOUT;
            if (_pmbus_check_byte_register(client, page,
                                           PMBUS_STATUS_VOUT))
                info->func[page] |= PMBUS_HAVE_STATUS_VOUT;
        }
        if (_pmbus_check_word_register(client, page, PMBUS_READ_IOUT)) {
            info->func[page] |= PMBUS_HAVE_IOUT;
            if (_pmbus_check_byte_register(client, 0,
                                           PMBUS_STATUS_IOUT))
                info->func[page] |= PMBUS_HAVE_STATUS_IOUT;
        }
        if (_pmbus_check_word_register(client, page, PMBUS_READ_POUT))
            info->func[page] |= PMBUS_HAVE_POUT;
    }
}

/*
 * Identify chip parameters.
 */
static int pmbus_identify(struct i2c_client *client,
                          struct pmbus_driver_info *info)
{
    int ret = 0;

    if (!info->pages) {
        /*
         * Check if the PAGE command is supported. If it is,
         * keep setting the page number until it fails or until the
         * maximum number of pages has been reached. Assume that
         * this is the number of pages supported by the chip.
         */
        if (_pmbus_check_byte_register(client, 0, PMBUS_PAGE)) {
            int page;

            for (page = 1; page < PMBUS_PAGES; page++) {
                if (_pmbus_set_page(client, page) < 0)
                    break;
            }
            _pmbus_set_page(client, 0);
            info->pages = page;
        } else {
            info->pages = 1;
        }
    }

    if (_pmbus_check_byte_register(client, 0, PMBUS_VOUT_MODE)) {
        int vout_mode;

        vout_mode = _pmbus_read_byte_data(client, 0, PMBUS_VOUT_MODE);
        if (vout_mode >= 0 && vout_mode != 0xff) {
            switch (vout_mode >> 5) {
            case 0:
                break;
            case 1:
                info->format[PSC_VOLTAGE_OUT] = vid;
                break;
            case 2:
                info->format[PSC_VOLTAGE_OUT] = direct;
                break;
            default:
                ret = -ENODEV;
                goto abort;
            }
        }
    }


    /*
     * We should check if the COEFFICIENTS register is supported.
     * If it is, and the chip is configured for direct mode, we can read
     * the coefficients from the chip, one set per group of sensor
     * registers.
     *
     * To do this, we will need access to a chip which actually supports the
     * COEFFICIENTS command, since the command is too complex to implement
     * without testing it. Until then, abort if a chip configured for direct
     * mode was detected.
     */
    if (info->format[PSC_VOLTAGE_OUT] == direct) {
        ret = -ENODEV;
        goto abort;
    }

    /* Try to find sensor groups  */
    pmbus_find_sensor_groups(client, info);
abort:
    return ret;
}

/*
 * Use driver_data to set the number of pages supported by the chip.
 */
static const struct i2c_device_id pmbus_id[] = {
    { "accton_ym2651", YM2651 },
    { "accton_ym2401", YM2401 },
    { "accton_ym2851", YM2851 },
    {}
};

static int limited_models(const struct i2c_device_id *id)
{
    int j;

    if (!id->name)
        return 0;

    for (j = 0; j < ARRAY_SIZE(pmbus_id); j++) {
        if (!pmbus_id[j].name)
            continue;

        if (!strcmp(id->name, pmbus_id[j].name))
        {
            if(id->driver_data < LIMITED_CHIPS)
            {
                return 1;
            }
            else
            {
                return 0;
            }
        }

    }

    return 0;
}

static int pmbus_probe(struct i2c_client *client,
                       const struct i2c_device_id *id)
{
    struct pmbus_driver_info *info;

    info = devm_kzalloc(&client->dev, sizeof(struct pmbus_driver_info),
                        GFP_KERNEL);
    if (!info)
        return -ENOMEM;

    info->identify = pmbus_identify;

    return _pmbus_do_probe(client, id, info);
}

int _pmbus_do_remove(struct i2c_client *client)
{
    struct pmbus_data *data = i2c_get_clientdata(client);
    hwmon_device_unregister(data->hwmon_dev);
    kfree(data->group.attrs);
    return 0;
}

MODULE_DEVICE_TABLE(i2c, pmbus_id);

/* This is the driver that will be inserted */
static struct i2c_driver pmbus_driver = {
    .driver = {
        .name = "accton_pmbus_3y",
    },
    .probe = pmbus_probe,
    .remove = _pmbus_do_remove,
    .id_table = pmbus_id,
};

module_i2c_driver(pmbus_driver);

MODULE_AUTHOR("Roy Lee<<EMAIL>>");
MODULE_DESCRIPTION("Accton PMBus driver for 3Y Power YM-2651Y.");
MODULE_LICENSE("GPL");
