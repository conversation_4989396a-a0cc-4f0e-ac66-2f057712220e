#!/usr/bin/python3

import os
import struct

NVRAM_RES = '/dev/nvram'
COLD_RESET = 0xE     # Cold Reset
WARM_RESET = 0x6     # Warm Reset

def io_reg_write(resource, offset, val):
    fd = os.open(resource, os.O_RDWR)
    if(fd < 0):
        print('file open failed %s" % resource')
        return
    if(os.lseek(fd, offset, os.SEEK_SET) != offset):
        print('lseek failed on %s' % resource)
        return
    ret = os.write(fd, struct.pack('B', val))
    if(ret != 1):
        print('write failed %d' % ret)
        return
    os.close(fd)

if __name__ == "__main__":
    io_reg_write(NVRAM_RES, 0x49, COLD_RESET)
